import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font
import json
import os
from datetime import datetime

class BudgetMonitor:
    def __init__(self, root):
        self.root = root
        self.root.title("Budget Monitor")
        self.root.geometry("900x700")
        self.root.configure(bg='#f0f0f0')
        
        # Data file path
        self.data_file = "budget_data.json"
        
        # Variables
        self.initial_budget = tk.DoubleVar()
        self.remaining_budget = tk.DoubleVar()
        self.expenses = []  # List of dictionaries: {'category': str, 'item': str, 'price': float, 'timestamp': str}
        
        # Fonts
        self.title_font = font.Font(family="Arial", size=14, weight="bold")
        self.label_font = font.Font(family="Arial", size=11)
        self.value_font = font.Font(family="Arial", size=12, weight="bold")
        
        # Load saved data first
        self.load_data()
        
        # Create widgets
        self.create_widgets()
        
        # Update display after loading data
        self.update_display()
        
        # Bind window close event to save data
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_widgets(self):
        # Main container
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title and Reset button frame
        title_frame = tk.Frame(main_frame, bg='#f0f0f0')
        title_frame.pack(fill='x', pady=(0, 20))
        
        title_label = tk.Label(title_frame, text="Budget Monitor", 
                              font=font.Font(family="Arial", size=18, weight="bold"),
                              bg='#f0f0f0', fg='#2c3e50')
        title_label.pack(side='left')
        
        # Reset button on the right
        reset_button = tk.Button(title_frame, text="Reset All Data", command=self.reset_all_data,
                               bg='#e74c3c', fg='white', font=self.label_font,
                               relief='raised', bd=2)
        reset_button.pack(side='right')
        
        # Top section - Budget info
        budget_frame = tk.Frame(main_frame, bg='#f0f0f0')
        budget_frame.pack(fill='x', pady=(0, 20))
        
        # Left side - Initial Budget
        left_frame = tk.Frame(budget_frame, bg='#e8f4fd', relief='ridge', bd=2)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        tk.Label(left_frame, text="Initial Budget", font=self.title_font,
                bg='#e8f4fd', fg='#2980b9').pack(pady=10)
        
        budget_input_frame = tk.Frame(left_frame, bg='#e8f4fd')
        budget_input_frame.pack(pady=10)
        
        self.budget_entry = tk.Entry(budget_input_frame, font=self.label_font, width=15)
        self.budget_entry.pack(side='left', padx=5)
        
        tk.Button(budget_input_frame, text="Set Budget", command=self.set_budget,
                 bg='#3498db', fg='white', font=self.label_font).pack(side='left', padx=5)
        
        self.initial_budget_label = tk.Label(left_frame, text="0,000 TND", 
                                           font=self.value_font, bg='#e8f4fd', fg='#2980b9')
        self.initial_budget_label.pack(pady=10)
        
        # Right side - Remaining Budget
        right_frame = tk.Frame(budget_frame, bg='#e8f8f5', relief='ridge', bd=2)
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        tk.Label(right_frame, text="Remaining Budget", font=self.title_font,
                bg='#e8f8f5', fg='#27ae60').pack(pady=10)
        
        self.remaining_budget_label = tk.Label(right_frame, text="0,000 TND", 
                                             font=self.value_font, bg='#e8f8f5', fg='#27ae60')
        self.remaining_budget_label.pack(pady=30)
        
        # Middle section - Add expenses
        expense_frame = tk.Frame(main_frame, bg='#fff2e6', relief='ridge', bd=2)
        expense_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(expense_frame, text="Add Expense", font=self.title_font,
                bg='#fff2e6', fg='#e67e22').pack(pady=10)
        
        input_frame = tk.Frame(expense_frame, bg='#fff2e6')
        input_frame.pack(pady=10)
        
        # Category input
        tk.Label(input_frame, text="Category:", font=self.label_font, bg='#fff2e6').grid(row=0, column=0, padx=5, pady=5, sticky='w')
        self.category_entry = tk.Entry(input_frame, font=self.label_font, width=20)
        self.category_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # Item name input
        tk.Label(input_frame, text="Item:", font=self.label_font, bg='#fff2e6').grid(row=0, column=2, padx=5, pady=5, sticky='w')
        self.item_entry = tk.Entry(input_frame, font=self.label_font, width=20)
        self.item_entry.grid(row=0, column=3, padx=5, pady=5)
        
        # Price input
        tk.Label(input_frame, text="Price (TND):", font=self.label_font, bg='#fff2e6').grid(row=1, column=0, padx=5, pady=5, sticky='w')
        self.price_entry = tk.Entry(input_frame, font=self.label_font, width=15)
        self.price_entry.grid(row=1, column=1, padx=5, pady=5)
        
        # Add button
        tk.Button(input_frame, text="Add Expense", command=self.add_expense,
                 bg='#e67e22', fg='white', font=self.label_font).pack(side='right', padx=10, pady=5)
        
        # Bind Enter key to add expense
        self.category_entry.bind('<Return>', lambda e: self.add_expense())
        self.item_entry.bind('<Return>', lambda e: self.add_expense())
        self.price_entry.bind('<Return>', lambda e: self.add_expense())
        self.budget_entry.bind('<Return>', lambda e: self.set_budget())
        
        # Expenses list
        list_frame = tk.Frame(main_frame, bg='#f8f9fa', relief='ridge', bd=2)
        list_frame.pack(fill='both', expand=True)
        
        tk.Label(list_frame, text="Expense History", font=self.title_font,
                bg='#f8f9fa', fg='#6c757d').pack(pady=10)
        
        # Frame for treeview and scrollbar
        tree_frame = tk.Frame(list_frame, bg='#f8f9fa')
        tree_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))
        
        # Treeview for expenses
        columns = ('Category', 'Item', 'Price', 'Date', 'Time')
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=10)
        
        self.tree.heading('Category', text='Category')
        self.tree.heading('Item', text='Item')
        self.tree.heading('Price', text='Price (TND)')
        self.tree.heading('Date', text='Date')
        self.tree.heading('Time', text='Time')
        
        self.tree.column('Category', width=120)
        self.tree.column('Item', width=180)
        self.tree.column('Price', width=100)
        self.tree.column('Date', width=80)
        self.tree.column('Time', width=80)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient='horizontal', command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Configure grid weights
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # Delete button
        delete_frame = tk.Frame(list_frame, bg='#f8f9fa')
        delete_frame.pack(pady=10)
        
        tk.Button(delete_frame, text="Delete Selected", command=self.delete_expense,
                 bg='#dc3545', fg='white', font=self.label_font).pack()
    
    def format_price(self, price):
        """Format price in TND format (3,000 TND)"""
        return f"{price:,.3f} TND"
    
    def parse_price(self, price_str):
        """Parse price string to float"""
        try:
            # Remove commas and convert to float
            cleaned = price_str.replace(',', '').replace(' ', '')
            return float(cleaned)
        except (ValueError, AttributeError):
            return 0.0
    
    def get_current_timestamp(self):
        """Get current date and time"""
        now = datetime.now()
        return {
            'full': now.strftime("%Y-%m-%d %H:%M:%S"),
            'date': now.strftime("%Y-%m-%d"),
            'time': now.strftime("%H:%M:%S")
        }
    
    def load_data(self):
        """Load data from JSON file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                self.initial_budget.set(data.get('initial_budget', 0.0))
                self.expenses = data.get('expenses', [])
                
                # Calculate remaining budget
                total_expenses = sum(expense['price'] for expense in self.expenses)
                self.remaining_budget.set(self.initial_budget.get() - total_expenses)
                
        except (json.JSONDecodeError, FileNotFoundError, KeyError) as e:
            # If file is corrupted or doesn't exist, start fresh
            print(f"Could not load data: {e}")
            self.initial_budget.set(0.0)
            self.remaining_budget.set(0.0)
            self.expenses = []
    
    def save_data(self):
        """Save data to JSON file"""
        try:
            data = {
                'initial_budget': self.initial_budget.get(),
                'expenses': self.expenses,
                'last_saved': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            messagebox.showerror("Error", f"Could not save data: {e}")
    
    def update_display(self):
        """Update all display elements"""
        # Update budget labels
        self.initial_budget_label.config(text=self.format_price(self.initial_budget.get()))
        
        remaining = self.remaining_budget.get()
        self.remaining_budget_label.config(text=self.format_price(remaining))
        
        # Change color if budget is exceeded
        if remaining < 0:
            self.remaining_budget_label.config(fg='#e74c3c')
        else:
            self.remaining_budget_label.config(fg='#27ae60')
        
        # Clear and populate treeview
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        for expense in self.expenses:
            timestamp = expense.get('timestamp', 'N/A')
            if timestamp and timestamp != 'N/A':
                try:
                    dt = datetime.fromisoformat(timestamp)
                    date_str = dt.strftime("%Y-%m-%d")
                    time_str = dt.strftime("%H:%M:%S")
                except (ValueError, TypeError):
                    date_str = "N/A"
                    time_str = "N/A"
            else:
                date_str = "N/A"
                time_str = "N/A"
            
            self.tree.insert('', 'end', values=(
                expense.get('category', ''),
                expense.get('item', ''),
                self.format_price(expense.get('price', 0.0)),
                date_str,
                time_str
            ))
    
    def set_budget(self):
        try:
            budget_str = self.budget_entry.get().strip()
            if not budget_str:
                messagebox.showerror("Error", "Please enter a budget amount")
                return
                
            budget = self.parse_price(budget_str)
            if budget <= 0:
                messagebox.showerror("Error", "Please enter a valid budget amount (greater than 0)")
                return
            
            self.initial_budget.set(budget)
            
            # Recalculate remaining budget
            total_expenses = sum(expense['price'] for expense in self.expenses)
            self.remaining_budget.set(budget - total_expenses)
            
            self.update_display()
            self.budget_entry.delete(0, tk.END)
            self.save_data()
            
        except Exception as e:
            messagebox.showerror("Error", f"Invalid budget amount: {e}")
    
    def add_expense(self):
        category = self.category_entry.get().strip()
        item = self.item_entry.get().strip()
        price_str = self.price_entry.get().strip()
        
        if not category or not item or not price_str:
            messagebox.showerror("Error", "Please fill in all fields")
            return
        
        try:
            price = self.parse_price(price_str)
            if price <= 0:
                messagebox.showerror("Error", "Please enter a valid price (greater than 0)")
                return
            
            # Check if expense exceeds remaining budget
            if self.initial_budget.get() > 0:
                if price > self.remaining_budget.get():
                    result = messagebox.askyesno("Budget Exceeded", 
                                               f"This expense ({self.format_price(price)}) exceeds your remaining budget "
                                               f"({self.format_price(self.remaining_budget.get())}). "
                                               f"Do you want to add it anyway?")
                    if not result:
                        return
            
            # Create expense record with timestamp
            expense = {
                'category': category,
                'item': item,
                'price': price,
                'timestamp': datetime.now().isoformat()
            }
            
            # Add expense
            self.expenses.append(expense)
            
            # Update remaining budget
            new_remaining = self.initial_budget.get() - sum(exp['price'] for exp in self.expenses)
            self.remaining_budget.set(new_remaining)
            
            self.update_display()
            
            # Clear input fields
            self.category_entry.delete(0, tk.END)
            self.item_entry.delete(0, tk.END)
            self.price_entry.delete(0, tk.END)
            
            # Focus on category field for next entry
            self.category_entry.focus_set()
            
            # Save data
            self.save_data()
            
        except Exception as e:
            messagebox.showerror("Error", f"Could not add expense: {e}")
    
    def delete_expense(self):
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showwarning("Warning", "Please select an expense to delete")
            return
        
        result = messagebox.askyesno("Confirm Delete", "Are you sure you want to delete the selected expense(s)?")
        if not result:
            return
        
        try:
            # Get indices of selected items (in reverse order to avoid index shifting)
            indices_to_delete = []
            for item in selected_items:
                index = self.tree.index(item)
                indices_to_delete.append(index)
            
            # Sort in descending order and delete
            for index in sorted(indices_to_delete, reverse=True):
                if 0 <= index < len(self.expenses):
                    del self.expenses[index]
            
            # Update remaining budget
            new_remaining = self.initial_budget.get() - sum(exp['price'] for exp in self.expenses)
            self.remaining_budget.set(new_remaining)
            
            self.update_display()
            self.save_data()
            
        except Exception as e:
            messagebox.showerror("Error", f"Could not delete expense: {e}")
    
    def reset_all_data(self):
        """Reset all data after confirmation"""
        result = messagebox.askyesno("Confirm Reset", 
                                   "Are you sure you want to reset all data? This will:\n\n"
                                   "• Clear your budget\n"
                                   "• Delete all expenses\n"
                                   "• Remove saved data file\n\n"
                                   "This action cannot be undone!")
        if not result:
            return
        
        try:
            # Reset all variables
            self.initial_budget.set(0.0)
            self.remaining_budget.set(0.0)
            self.expenses = []
            
            # Clear input fields
            self.budget_entry.delete(0, tk.END)
            self.category_entry.delete(0, tk.END)
            self.item_entry.delete(0, tk.END)
            self.price_entry.delete(0, tk.END)
            
            # Update display
            self.update_display()
            
            # Remove data file
            if os.path.exists(self.data_file):
                os.remove(self.data_file)
            
            messagebox.showinfo("Reset Complete", "All data has been reset successfully!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Could not reset data: {e}")
    
    def on_closing(self):
        """Handle window closing event"""
        try:
            self.save_data()
        except Exception as e:
            print(f"Error saving data on close: {e}")
        finally:
            self.root.destroy()

def main():
    root = tk.Tk()
    app = BudgetMonitor(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"Application error: {e}")

if __name__ == "__main__":
    main()